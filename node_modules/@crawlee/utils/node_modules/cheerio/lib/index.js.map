{"version": 3, "file": "index.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA;;;;GAIG;AACH,6CAA2B;AAc3B,qCAAoC;AACpC,uCAAsC;AACtC,iEAAgF;AAChF,kEAAmD;AACnD,2CAAoE;AAEpE,IAAM,KAAK,GAAG,IAAA,mBAAQ,EAAC,UAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO;IAC3D,OAAA,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe;QACxC,CAAC,CAAC,IAAA,2BAAoB,EAAC,OAAO,EAAE,OAAO,CAAC;QACxC,CAAC,CAAC,IAAA,mCAAe,EAAC,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC;AAF1D,CAE0D,CAC3D,CAAC;AAEF,0EAA0E;AAC1E;;;;;;;;;;;;GAYG;AACU,QAAA,IAAI,GAAG,IAAA,iBAAO,EAAC,KAAK,EAAE,UAAC,GAAG,EAAE,OAAO;IAC9C,OAAA,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe;QACxC,CAAC,CAAC,IAAA,wBAAqB,EAAC,GAAG,EAAE,OAAO,CAAC;QACrC,CAAC,CAAC,IAAA,oCAAgB,EAAC,GAAG,CAAC;AAFzB,CAEyB,CAC1B,CAAC;AAEF;;;;GAIG;AACH,kBAAe,IAAA,YAAI,EAAC,EAAE,CAAC,CAAC;AAExB,yCAA8C;AAArC,iGAAA,IAAI,OAAA;AAAE,gGAAA,GAAG,OAAA;AAAE,iGAAA,IAAI,OAAA;AAExB,yDAA6C;AAE7C;;;;;;;;;;;;;;;;;;GAkBG;AACY,QAAA,QAAQ,GAAK,aAAa,UAAC;AAE1C;;;;;;;;;;;;;GAaG;AACY,QAAA,KAAK,GAAK,aAAa,OAAC;AAEvC;;;;;;;;;;;;GAYG;AACY,QAAA,SAAS,GAAK,aAAa,WAAC;AAE3C;;;;;;;;;;;GAWG;AACY,QAAA,IAAI,GAAK,aAAa,MAAC"}