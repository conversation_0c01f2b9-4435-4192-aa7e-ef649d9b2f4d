{"version": 3, "file": "css.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["api/css.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAwD7C;;;;;;;;GAQG;AACH,MAAM,UAAU,GAAG,CAEjB,IAAiD,EACjD,GAEqE;IAErE,IACE,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC;QAC7B,kCAAkC;QAClC,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAClD;QACA,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE;YAC7B,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;gBACb,yCAAyC;gBACzC,MAAM,CAAC,EAAE,EAAE,IAAc,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;aACpC;QACH,CAAC,CAAC,CAAC;KACJ;IAED,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACrB,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAc,CAAC,CAAC;AACzC,CAAC;AAED;;;;;;;;GAQG;AACH,SAAS,MAAM,CACb,EAAW,EACX,IAAqC,EACrC,KAGa,EACb,GAAW;IAEX,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;QAE1B,MAAM,GAAG,GACP,OAAO,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAE1E,IAAI,GAAG,KAAK,EAAE,EAAE;YACd,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;SACrB;aAAM,IAAI,GAAG,IAAI,IAAI,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;SACpB;QAED,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;KACzC;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACjC,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;KACJ;AACH,CAAC;AAsBD,SAAS,MAAM,CACb,EAAW,EACX,IAAwB;IAExB,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;QAAE,OAAO;IAE9B,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;IAC1C,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;KACrB;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACvB,MAAM,SAAS,GAA2B,EAAE,CAAC;QAC7C,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACpB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE;gBACxB,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;aAChC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,SAAS,CAAC,GAA2B;IAC5C,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAC5B,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,EAC9D,EAAE,CACH,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,KAAK,CAAC,MAAc;IAC3B,MAAM,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;IAE/B,IAAI,CAAC,MAAM;QAAE,OAAO,EAAE,CAAC;IAEvB,MAAM,GAAG,GAA2B,EAAE,CAAC;IAEvC,IAAI,GAAuB,CAAC;IAE5B,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QACnC,MAAM,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC3B,2FAA2F;QAC3F,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACjC,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC;YAC9B,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,SAAS,EAAE;gBAC3C,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,EAAE,CAAC;aAC3B;SACF;aAAM;YACL,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC7B,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SACpC;KACF;IAED,OAAO,GAAG,CAAC;AACb,CAAC"}