/** @file Alternative Entry point for Cheerio, excluding parse5. */
/// <reference types="node" />
export type { Cheerio, CheerioAPI, CheerioOptions, HTMLParser2Options, Node, AnyNode, ParentNode, Element, Document, } from '.';
/**
 * Types used in signatures of Cheerio methods.
 *
 * @category Cheerio
 */
export * from './types.js';
/**
 * Create a querying function, bound to a document created from the provided markup.
 *
 * @param content - Markup to be loaded.
 * @param options - Options for the created instance.
 * @param isDocument - Always `false` here, as we are always using `htmlparser2`.
 * @returns The loaded document.
 * @see {@link https://cheerio.js.org#loading} for additional usage information.
 */
export declare const load: (content: string | import("domhandler/lib/node").AnyNode | import("domhandler/lib/node").AnyNode[] | Buffer, options?: import("./options").CheerioOptions | null | undefined, isDocument?: boolean) => import("./load.js").CheerioAPI;
//# sourceMappingURL=slim.d.ts.map