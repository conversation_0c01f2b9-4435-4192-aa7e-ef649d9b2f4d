{"version": 3, "file": "cheerio.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["cheerio.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,UAAU,MAAM,qBAAqB,CAAC;AAClD,OAAO,KAAK,UAAU,MAAM,qBAAqB,CAAC;AAClD,OAAO,KAAK,YAAY,MAAM,uBAAuB,CAAC;AACtD,OAAO,KAAK,GAAG,MAAM,cAAc,CAAC;AACpC,OAAO,KAAK,KAAK,MAAM,gBAAgB,CAAC;AAQxC,MAAM,OAAgB,OAAO;IAY3B;;;;;;;;OAQG;IACH,YACE,QAAkC,EAClC,IAA8B,EAC9B,OAAwB;QAvB1B,WAAM,GAAG,CAAC,CAAC;QAyBT,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAElB,IAAI,QAAQ,EAAE;YACZ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;gBAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;SAC/B;IACH,CAAC;CAwCF;AAcD,qCAAqC;AACrC,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,kBAAkB,CAAC;AAE/C;;GAEG;AACH,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC;AAElD,mDAAmD;AACnD,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAEtE,kBAAkB;AAClB,MAAM,CAAC,MAAM,CACX,OAAO,CAAC,SAAS,EACjB,UAAU,EACV,UAAU,EACV,YAAY,EACZ,GAAG,EACH,KAAK,CACN,CAAC"}