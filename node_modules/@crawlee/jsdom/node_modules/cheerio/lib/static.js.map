{"version": 3, "file": "static.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["static.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,qCAAuC;AACvC,yDAKsB;AAEtB;;;;;;;GAOG;AACH,SAAS,MAAM,CACb,IAAgB,EAChB,GAA4C,EAC5C,OAAwB;IAExB,IAAI,CAAC,IAAI;QAAE,OAAO,EAAE,CAAC;IAErB,OAAO,IAAI,CAAC,GAAG,aAAH,GAAG,cAAH,GAAG,GAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;AAC/E,CAAC;AAED;;;;;GAKG;AACH,SAAS,SAAS,CAChB,GAAyD,EACzD,OAAwB;IAExB,OAAO,CACL,CAAC,OAAO;QACR,OAAO,GAAG,KAAK,QAAQ;QACvB,GAAG,IAAI,IAAI;QACX,CAAC,CAAC,QAAQ,IAAI,GAAG,CAAC;QAClB,CAAC,CAAC,MAAM,IAAI,GAAG,CAAC,CACjB,CAAC;AACJ,CAAC;AAqBD,SAAgB,IAAI,CAElB,GAAkD,EAClD,OAAwB;IAExB;;;;;OAKG;IACH,IAAM,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAErE;;;OAGG;IACH,IAAM,IAAI,kCACL,oBAAc,GACd,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,GACd,IAAA,oBAAc,EAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC,CACjC,CAAC;IAEF,OAAO,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AACtC,CAAC;AAxBD,oBAwBC;AAED;;;;;GAKG;AACH,SAAgB,GAAG,CAEjB,GAAiC;IAEjC,IAAM,OAAO,yBAAQ,IAAI,CAAC,QAAQ,KAAE,OAAO,EAAE,IAAI,GAAE,CAAC;IAEpD,OAAO,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AACpC,CAAC;AAPD,kBAOC;AAED;;;;;;;;;GASG;AACH,SAAgB,IAAI,CAElB,QAA6B;IAE7B,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IAE5D,IAAI,GAAG,GAAG,EAAE,CAAC;IAEb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,GAAG,IAAI,IAAA,sBAAW,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;KAC9B;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAbD,oBAaC;AAqBD,SAAgB,SAAS,CAEvB,IAAoB,EACpB,OAA2B,EAC3B,WAA4D;IAA5D,4BAAA,EAAA,cAAc,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;IAE5D,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACrC,OAAO,IAAI,CAAC;KACb;IAED,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE;QAChC,WAAW,GAAG,OAAO,CAAC;KACvB;IAED,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,oBAAc,EAAE,KAAK,CAAC,CAAC;IACtD,IAAI,CAAC,WAAW,EAAE;QAChB,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,CAAC;KAC3B;IAED;;;;;;OAMG;IACH,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;AAC3C,CAAC;AA3BD,8BA2BC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,IAAI;IAClB,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B,CAAC;AAFD,oBAEC;AAED;;;;;;;;;GASG;AACH,SAAgB,QAAQ,CAAC,SAAkB,EAAE,SAAkB;IAC7D,oEAAoE;IACpE,IAAI,SAAS,KAAK,SAAS,EAAE;QAC3B,OAAO,KAAK,CAAC;KACd;IAED;;;OAGG;IACH,IAAI,IAAI,GAAmB,SAAS,CAAC;IACrC,OAAO,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE;QACnC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;QACnB,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAnBD,4BAmBC;AAOD;;;;;;;;GAQG;AACH,SAAgB,KAAK,CACnB,IAA0B,EAC1B,IAAkB;IAElB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;QAC5C,OAAO;KACR;IACD,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC;IAC5B,IAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;IAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;QAC5B,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;KAC7B;IACD,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IACxB,OAAO,IAAI,CAAC;AACd,CAAC;AAfD,sBAeC;AAED;;;;;GAKG;AACH,SAAS,WAAW,CAAC,IAAS;IAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;QACvB,OAAO,IAAI,CAAC;KACb;IAED,IACE,OAAO,IAAI,KAAK,QAAQ;QACxB,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC;QACrD,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;QAC/B,IAAI,CAAC,MAAM,GAAG,CAAC,EACf;QACA,OAAO,KAAK,CAAC;KACd;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE;YAChB,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}