import { AnyNode, Document, ParentNode } from 'domhandler';
import type { InternalOptions } from '../options.js';
/**
 * Parse the content with `parse5` in the context of the given `ParentNode`.
 *
 * @param content - The content to parse.
 * @param options - A set of options to use to parse.
 * @param isDocument - Whether to parse the content as a full HTML document.
 * @param context - The context in which to parse the content.
 * @returns The parsed content.
 */
export declare function parseWithParse5(content: string, options: InternalOptions, isDocument: boolean, context: ParentNode | null): Document;
/**
 * Renders the given DOM tree with `parse5` and returns the result as a string.
 *
 * @param dom - The DOM tree to render.
 * @returns The rendered document.
 */
export declare function renderWithParse5(dom: AnyNode | ArrayLike<AnyNode>): string;
//# sourceMappingURL=parse5-adapter.d.ts.map