{"version": 3, "file": "types.d.ts", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["types.ts"], "names": [], "mappings": "AAAA,aAAK,gBAAgB,GACjB,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON><PERSON>,<PERSON>CH,<PERSON><PERSON>,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON>G,GACH,<PERSON>G,<PERSON>CH,<PERSON>G,GACH,GAAG,GACH,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,<PERSON>G,<PERSON>CH,GAAG,GACH,GAAG,CAAC;AAER,aAAK,YAAY,GACb,gBAAgB,GAChB,SAAS,CAAC,gBAAgB,CAAC,GAC3B,GAAG,MAAM,EAAE,CAAC;AAEhB,aAAK,eAAe,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,<PERSON>G,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACrE;;;GAGG;AACH,oBAAY,YAAY,GACpB,GAAG,eAAe,GAAG,YAAY,GAAG,MAAM,EAAE,GAC5C,GAAG,YAAY,GAAG,MAAM,EAAE,CAAC;AAE/B,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAC5C,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAE1C,2DAA2D;AAC3D,oBAAY,kBAAkB,CAAC,CAAC,SAAS,OAAO,IAC5C,OAAO,CAAC,CAAC,CAAC,GACV,CAAC,EAAE,GACH,CAAC,GACD,MAAM,CAAC;AACX,gFAAgF;AAChF,oBAAY,aAAa,CAAC,CAAC,SAAS,OAAO,IACvC,kBAAkB,CAAC,CAAC,CAAC,GACrB,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,KAAK,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;AAE3D,iDAAiD;AACjD,oBAAY,cAAc,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,KAAK,OAAO,CAAC;AACvE,qDAAqD;AACrD,oBAAY,eAAe,CAAC,CAAC,IAAI,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC"}