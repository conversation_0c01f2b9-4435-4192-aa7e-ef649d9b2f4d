{"version": 3, "file": "attributes.js", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["api/attributes.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,0CAAoC;AACpC,wCAAiE;AAGjE,qCAAkD;AAClD,IAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC/C,IAAM,MAAM,GAAG,KAAK,CAAC;AACrB,IAAM,cAAc,GAAG,OAAO,CAAC;AAC/B;;;GAGG;AACH,IAAM,UAAU,GAA4B;IAC1C,IAAI,EAAE,IAAI;IACV,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,KAAK;CACb,CAAC;AACF,+BAA+B;AAC/B,IAAM,QAAQ,GACZ,6HAA6H,CAAC;AAChI,wDAAwD;AACxD,IAAM,MAAM,GAAG,oBAAoB,CAAC;AAwBpC,SAAS,OAAO,CACd,IAAa,EACb,IAAwB,EACxB,OAAiB;;IAEjB,IAAI,CAAC,IAAI,IAAI,CAAC,IAAA,gBAAK,EAAC,IAAI,CAAC;QAAE,OAAO,SAAS,CAAC;IAE5C,MAAA,IAAI,CAAC,OAAO,oCAAZ,IAAI,CAAC,OAAO,GAAK,EAAE,EAAC;IAEpB,6DAA6D;IAC7D,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,IAAI,CAAC,OAAO,CAAC;KACrB;IAED,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;QACnC,8BAA8B;QAC9B,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACpE;IAED,gEAAgE;IAChE,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,OAAO,EAAE;QAC9C,OAAO,IAAA,gBAAI,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC5B;IAED,qDAAqD;IACrD,IACE,IAAI,CAAC,IAAI,KAAK,OAAO;QACrB,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,UAAU,CAAC;QACzE,IAAI,KAAK,OAAO,EAChB;QACA,OAAO,IAAI,CAAC;KACb;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,OAAO,CAAC,EAAW,EAAE,IAAY,EAAE,KAAoB;IAC9D,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,eAAe,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;KAC3B;SAAM;QACL,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,UAAG,KAAK,CAAE,CAAC;KAC/B;AACH,CAAC;AAuFD,SAAgB,IAAI,CAElB,IAA6C,EAC7C,KAGiE;IAEjE,wCAAwC;IACxC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QACnD,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B;oBACE,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;iBAClD;aACF;YACD,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE,EAAE,CAAC;gBACzB,IAAI,IAAA,gBAAK,EAAC,EAAE,CAAC;oBAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE;YACtB,IAAI,CAAC,IAAA,gBAAK,EAAC,EAAE,CAAC;gBAAE,OAAO;YAEvB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,OAAO;oBAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC/B,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;gBACjC,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC,EAAE,EAAE,IAAc,EAAE,KAAe,CAAC,CAAC;aAC9C;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC;QACzB,CAAC,CAAC,IAAI;QACN,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAc,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC7D,CAAC;AArCD,oBAqCC;AAED;;;;;;;;GAQG;AACH,SAAS,OAAO,CACd,EAAW,EACX,IAAY,EACZ,OAAiB;IAEjB,OAAO,IAAI,IAAI,EAAE;QACf,CAAC,CAAC,yEAAyE;YACzE,EAAE,CAAC,IAAI,CAAC;QACV,CAAC,CAAC,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YACjC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,SAAS;YACxC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACjC,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,OAAO,CAAC,EAAW,EAAE,IAAY,EAAE,KAAc,EAAE,OAAiB;IAC3E,IAAI,IAAI,IAAI,EAAE,EAAE;QACd,oCAAoC;QACpC,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;KAClB;SAAM;QACL,OAAO,CACL,EAAE,EACF,IAAI,EACJ,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAG,KAAK,CAAE,CACnE,CAAC;KACH;AACH,CAAC;AAmFD,SAAgB,IAAI,CAElB,IAAwE,EACxE,KAMW;IATb,iBA4GC;;IAjGC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QACnD,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAEnB,IAAI,CAAC,EAAE,IAAI,CAAC,IAAA,gBAAK,EAAC,EAAE,CAAC;YAAE,OAAO,SAAS,CAAC;QAExC,QAAQ,IAAI,EAAE;YACZ,KAAK,OAAO,CAAC,CAAC;gBACZ,IAAM,UAAQ,GAAG,IAAI,CAAC,GAAG,EAAe,CAAC;gBACzC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,UAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,OAAO,CAAC,UAAC,CAAC,EAAE,CAAC;oBAChB,UAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;gBAEH,UAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAE9B,OAAO,UAAQ,CAAC;aACjB;YACD,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC,CAAC;gBACf,OAAO,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;aAC9B;YAED,KAAK,MAAM,CAAC;YACZ,KAAK,KAAK,CAAC,CAAC;gBACV,IAAM,MAAI,GAAG,MAAA,EAAE,CAAC,OAAO,0CAAG,IAAI,CAAC,CAAC;gBAEhC,+DAA+D;gBAC/D,IACE,OAAO,GAAG,KAAK,WAAW;oBAC1B,CAAC,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC,EAAE,CAAC,OAAO,KAAK,GAAG,IAAI,EAAE,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;wBAC9D,CAAC,IAAI,KAAK,KAAK;4BACb,CAAC,EAAE,CAAC,OAAO,KAAK,KAAK;gCACnB,EAAE,CAAC,OAAO,KAAK,QAAQ;gCACvB,EAAE,CAAC,OAAO,KAAK,OAAO;gCACtB,EAAE,CAAC,OAAO,KAAK,OAAO;gCACtB,EAAE,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC;oBAChC,MAAI,KAAK,SAAS;oBAClB,IAAI,CAAC,OAAO,CAAC,OAAO,EACpB;oBACA,OAAO,IAAI,GAAG,CAAC,MAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;iBACjD;gBACD,8DAA8D;gBAE9D,OAAO,MAAI,CAAC;aACb;YAED,KAAK,WAAW,CAAC,CAAC;gBAChB,OAAO,IAAA,oBAAS,EAAC,EAAE,CAAC,CAAC;aACtB;YAED,KAAK,aAAa,CAAC,CAAC;gBAClB,OAAO,IAAA,sBAAW,EAAC,EAAE,CAAC,CAAC;aACxB;YAED,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;YAE5D,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;YAErB;gBACE,OAAO,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;SAClD;KACF;IAED,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QACnD,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC/B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;aAClD;YACD,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE,EAAE,CAAC;gBACzB,IAAI,IAAA,gBAAK,EAAC,EAAE,CAAC,EAAE;oBACb,OAAO,CACL,EAAE,EACF,IAAI,EACJ,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAC1D,KAAI,CAAC,OAAO,CAAC,OAAO,CACrB,CAAC;iBACH;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE;YACtB,IAAI,CAAC,IAAA,gBAAK,EAAC,EAAE,CAAC;gBAAE,OAAO;YAEvB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;oBAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;oBACtB,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAChD;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AA5GD,oBA4GC;AAYD;;;;;;;GAOG;AACH,SAAS,OAAO,CACd,EAAW,EACX,IAAsC,EACtC,KAAe;;IAEf,IAAM,IAAI,GAAgB,EAAE,CAAC;IAE7B,MAAA,IAAI,CAAC,IAAI,oCAAT,IAAI,CAAC,IAAI,GAAK,EAAE,EAAC;IAEjB,IAAI,OAAO,IAAI,KAAK,QAAQ;QAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACxD,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QACxD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;KACzB;AACH,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,QAAQ,CAAC,EAAe,EAAE,IAAa;IAC9C,IAAI,QAAQ,CAAC;IACb,IAAI,OAAO,CAAC;IACZ,IAAI,KAAK,CAAC;IAEV,IAAI,IAAI,IAAI,IAAI,EAAE;QAChB,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,UAAC,QAAQ;YACjD,OAAA,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC;QAAnC,CAAmC,CACpC,CAAC;QACF,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAC,OAAO;YAC7B,OAAA,IAAA,oBAAS,EAAC,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAA/C,CAA+C,CAChD,CAAC;KACH;SAAM;QACL,QAAQ,GAAG,CAAC,cAAc,GAAG,IAAA,kBAAO,EAAC,IAAI,CAAC,CAAC,CAAC;QAC5C,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;KAClB;IAED,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE;QAC9C,IAAM,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5B,IACE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;YAChC,CAAC,MAAM,CAAC,IAAI,CAAE,EAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,EAC9C;YACA,KAAK,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE5B,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,EAAE;gBAClC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;aAC3B;iBAAM,IAAI,KAAK,KAAK,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC1C,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;aACvB;iBAAM,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC7B,IAAI;oBACF,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;iBAC3B;gBAAC,OAAO,CAAC,EAAE;oBACV,YAAY;iBACb;aACF;YAEA,EAAE,CAAC,IAAgC,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;SACtD;KACF;IAED,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AACxC,CAAC;AAoFD,SAAgB,IAAI,CAElB,IAAuC,EACvC,KAAe;;IAEf,IAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAErB,IAAI,CAAC,IAAI,IAAI,CAAC,IAAA,gBAAK,EAAC,IAAI,CAAC;QAAE,OAAO;IAElC,IAAM,MAAM,GAAgB,IAAI,CAAC;IACjC,MAAA,MAAM,CAAC,IAAI,oCAAX,MAAM,CAAC,IAAI,GAAK,EAAE,EAAC;IAEnB,qDAAqD;IACrD,IAAI,CAAC,IAAI,EAAE;QACT,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC;KACzB;IAED,wCAAwC;IACxC,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,KAAK,SAAS,EAAE;QACnD,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE;YACf,IAAI,IAAA,gBAAK,EAAC,EAAE,CAAC,EAAE;gBACb,IAAI,OAAO,IAAI,KAAK,QAAQ;oBAAE,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;;oBAC3C,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAgB,CAAC,CAAC;aAC1C;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;KACb;IACD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;QAClC,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1B;IAED,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAChC,CAAC;AAhCD,oBAgCC;AAwCD,SAAgB,GAAG,CAEjB,KAAyB;IAEzB,IAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC;IACxC,IAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IAExB,IAAI,CAAC,OAAO,IAAI,CAAC,IAAA,gBAAK,EAAC,OAAO,CAAC;QAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;IAEpE,QAAQ,OAAO,CAAC,IAAI,EAAE;QACpB,KAAK,UAAU;YACb,OAAO,IAAI,CAAC,IAAI,CAAC,KAAe,CAAC,CAAC;QACpC,KAAK,QAAQ,CAAC,CAAC;YACb,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC5C,IAAI,CAAC,QAAQ,EAAE;gBACb,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;oBAC9D,OAAO,IAAI,CAAC;iBACb;gBAED,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBAE3C,IAAM,MAAM,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;gBAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACtC,IAAI,CAAC,IAAI,CAAC,yBAAiB,MAAM,CAAC,CAAC,CAAC,QAAI,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;iBAChE;gBAED,OAAO,IAAI,CAAC;aACb;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC1B,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,UAAC,EAAE,IAAK,OAAA,IAAA,gBAAI,EAAC,EAAE,CAAC,QAAQ,CAAC,EAAjB,CAAiB,CAAC;gBACjD,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC1B;QACD,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ;YACX,OAAO,QAAQ;gBACb,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;gBACpB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAe,CAAC,CAAC;KAC3C;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAzCD,kBAyCC;AAED;;;;;;GAMG;AACH,SAAS,eAAe,CAAC,IAAa,EAAE,IAAY;IAClD,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;QAAE,OAAO;IAE9D,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;;GAMG;AACH,SAAS,UAAU,CAAC,KAAc;IAChC,OAAO,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AACjD,CAAC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,UAAU,CAExB,IAAY;IAEZ,IAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;4BAE1B,CAAC;QACR,IAAA,kBAAO,UAAO,UAAC,IAAI;YACjB,IAAI,IAAA,gBAAK,EAAC,IAAI,CAAC;gBAAE,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;;;IAHL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;gBAAhC,CAAC;KAIT;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAbD,gCAaC;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,SAAgB,QAAQ,CAEtB,SAAiB;IAEjB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,UAAC,IAAI;QAC9B,IAAM,KAAK,GAAG,IAAA,gBAAK,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACnD,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;QAEb,IAAI,KAAK,IAAI,SAAS,CAAC,MAAM,EAAE;YAC7B,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;gBACrD,IAAM,GAAG,GAAG,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC;gBAEnC,IACE,CAAC,GAAG,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC1C,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EACjD;oBACA,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AACL,CAAC;AAvBD,4BAuBC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,SAAgB,QAAQ,CAEtB,KAEyE;IAEzE,oBAAoB;IACpB,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;QAC/B,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE,EAAE,CAAC;YACzB,IAAI,IAAA,gBAAK,EAAC,EAAE,CAAC,EAAE;gBACb,IAAM,SAAS,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBAC5C,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC;aACnD;QACH,CAAC,CAAC,CAAC;KACJ;IAED,iDAAiD;IACjD,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,IAAI,CAAC;IAErD,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACvC,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;IAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;QACpC,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB,2CAA2C;QAC3C,IAAI,CAAC,IAAA,gBAAK,EAAC,EAAE,CAAC;YAAE,SAAS;QAEzB,wGAAwG;QACxG,IAAM,SAAS,GAAG,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;QAE9C,IAAI,CAAC,SAAS,EAAE;YACd,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;SACnD;aAAM;YACL,IAAI,QAAQ,GAAG,WAAI,SAAS,MAAG,CAAC;YAEhC,gCAAgC;YAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAM,WAAW,GAAG,UAAG,UAAU,CAAC,CAAC,CAAC,MAAG,CAAC;gBACxC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAI,WAAW,CAAE,CAAC;oBAAE,QAAQ,IAAI,WAAW,CAAC;aACpE;YAED,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;SACvC;KACF;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA9CD,4BA8CC;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,SAAgB,WAAW,CAEzB,IAEyE;IAEzE,gCAAgC;IAChC,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;QAC9B,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE,EAAE,CAAC;YACzB,IAAI,IAAA,gBAAK,EAAC,EAAE,CAAC,EAAE;gBACb,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aACrE;QACH,CAAC,CAAC,CAAC;KACJ;IAED,IAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IACjC,IAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC;IAClC,IAAM,SAAS,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC;IAEzC,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE;QACtB,IAAI,CAAC,IAAA,gBAAK,EAAC,EAAE,CAAC;YAAE,OAAO;QAEvB,IAAI,SAAS,EAAE;YACb,4DAA4D;YAC5D,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;SAC1B;aAAM;YACL,IAAM,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YAClD,IAAI,OAAO,GAAG,KAAK,CAAC;YAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;gBACnC,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;gBAE5C,IAAI,KAAK,IAAI,CAAC,EAAE;oBACd,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;oBAC3B,OAAO,GAAG,IAAI,CAAC;oBAEf;;;uBAGG;oBACH,CAAC,EAAE,CAAC;iBACL;aACF;YACD,IAAI,OAAO,EAAE;gBACX,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAC3C;SACF;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAhDD,kCAgDC;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,SAAgB,WAAW,CAEzB,KAOgB,EAChB,QAAkB;IAElB,oBAAoB;IACpB,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;QAC/B,OAAO,IAAA,kBAAO,EAAC,IAAI,EAAE,UAAC,EAAE,EAAE,CAAC;YACzB,IAAI,IAAA,gBAAK,EAAC,EAAE,CAAC,EAAE;gBACb,WAAW,CAAC,IAAI,CACd,CAAC,EAAE,CAAC,EACJ,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,EACtD,QAAQ,CACT,CAAC;aACH;QACH,CAAC,CAAC,CAAC;KACJ;IAED,iDAAiD;IACjD,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,IAAI,CAAC;IAErD,IAAM,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACvC,IAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;IACrC,IAAM,KAAK,GAAG,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtE,IAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC;IAEhC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;QACpC,IAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACnB,2CAA2C;QAC3C,IAAI,CAAC,IAAA,gBAAK,EAAC,EAAE,CAAC;YAAE,SAAS;QAEzB,IAAM,cAAc,GAAG,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;QAEvD,gCAAgC;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACnC,+CAA+C;YAC/C,IAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;YAEpD,sEAAsE;YACtE,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;gBAC3B,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;aACpC;iBAAM,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;gBACnC,+CAA+C;gBAC/C,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aACjC;SACF;QAED,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAChD;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA1DD,kCA0DC"}