{"version": 3, "file": "traversing.d.ts", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["api/traversing.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EACL,OAAO,EACP,OAAO,EAGP,QAAQ,EACT,MAAM,YAAY,CAAC;AACpB,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAW7C,OAAO,KAAK,EAAkB,eAAe,EAAE,MAAM,aAAa,CAAC;AAGnE;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,OAAO,EACpC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,kBAAkB,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GACvD,OAAO,CAAC,OAAO,CAAC,CAkClB;AA2HD;;;;;;;;;;;;;;;GAeG;AACH,eAAO,MAAM,MAAM,mDAxHF,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CA0HrB,CAAC;AAEF;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,OAAO,mDA/IH,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CAyJrB,CAAC;AAEF;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,YAAY,mDA7FV,gBAAgB,OAAO,CAAC,GAAG,IAAI,mBACzB,gBAAgB,OAAO,CAAC,KACxC,QAAQ,OAAO,CA+FnB,CAAC;AAEF;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,OAAO,EACvC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,QAAQ,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,GAClC,OAAO,CAAC,OAAO,CAAC,CA+BlB;AAED;;;;;;;;;;;;;;GAcG;AACH,eAAO,MAAM,IAAI,mDA/PA,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CA8PgD,CAAC;AAEvE;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,OAAO,mDAnRH,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CAyRD,CAAC;AAEtB;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,SAAS,mDA7NP,gBAAgB,OAAO,CAAC,GAAG,IAAI,mBACzB,gBAAgB,OAAO,CAAC,KACxC,QAAQ,OAAO,CA8NnB,CAAC;AAEF;;;;;;;;;;;;;;;GAeG;AACH,eAAO,MAAM,IAAI,mDAlUA,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CAiUgD,CAAC;AAEvE;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,OAAO,mDAvVH,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CA6VD,CAAC;AAEtB;;;;;;;;;;;;;;;;GAgBG;AACH,eAAO,MAAM,SAAS,mDAjSP,gBAAgB,OAAO,CAAC,GAAG,IAAI,mBACzB,gBAAgB,OAAO,CAAC,KACxC,QAAQ,OAAO,CAkSnB,CAAC;AAEF;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAM,QAAQ,mDAzYJ,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CA4YrB,CAAC;AAEF;;;;;;;;;;;;;;;;;GAiBG;AACH,eAAO,MAAM,QAAQ,mDAjaJ,gBAAgB,OAAO,CAAC,KAClC,QAAQ,OAAO,CAmarB,CAAC;AAEF;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,QAAQ,CAAC,CAAC,SAAS,OAAO,EACxC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GACf,OAAO,CAAC,OAAO,CAAC,CAOlB;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAgB,IAAI,CAAC,CAAC,EACpB,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,KAAK,IAAI,GAAG,OAAO,GAChD,OAAO,CAAC,CAAC,CAAC,CAKZ;AAED;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,wBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EACtB,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,SAAS,GAC5D,OAAO,CAAC,CAAC,CAAC,CAUZ;AAsBD;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,wBAAgB,MAAM,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,EACnC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,KAAK,KAAK,IAAI,CAAC,GACtD,OAAO,CAAC,CAAC,CAAC,CAAC;AACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,wBAAgB,MAAM,CAAC,CAAC,EAAE,CAAC,SAAS,eAAe,CAAC,CAAC,CAAC,EACpD,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,KAAK,EAAE,CAAC,GACP,OAAO,CAAC,CAAC,SAAS,MAAM,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;AAU3C,wBAAgB,WAAW,CAAC,CAAC,EAC3B,KAAK,EAAE,CAAC,EAAE,EACV,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,EACzB,OAAO,CAAC,EAAE,OAAO,EACjB,IAAI,CAAC,EAAE,QAAQ,GACd,OAAO,EAAE,GAAG,CAAC,EAAE,CAIjB;AAED;;;;;;;;;;;GAWG;AACH,wBAAgB,EAAE,CAAC,CAAC,EAClB,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,QAAQ,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,GAC5B,OAAO,CAWT;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgCG;AACH,wBAAgB,GAAG,CAAC,CAAC,SAAS,OAAO,EACnC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC,GACxB,OAAO,CAAC,CAAC,CAAC,CAYZ;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,wBAAgB,GAAG,CACjB,IAAI,EAAE,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,EAChC,kBAAkB,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GACtD,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,CAO5B;AAED;;;;;;;;;;;;;GAaG;AACH,wBAAgB,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAErE;AAED;;;;;;;;;;;;;GAaG;AACH,wBAAgB,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAEpD;AAED;;;;;;;;;;;;;;;;;;GAkBG;AACH,wBAAgB,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAQ7D;AAED;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;AACnE;;;;;;;;;;;;;GAaG;AACH,wBAAgB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;AAQ9C;;;;;;;;;;;GAWG;AACH,wBAAgB,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAEhD;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,KAAK,CAAC,CAAC,SAAS,OAAO,EACrC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,gBAAgB,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,GACrD,MAAM,CAmBR;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAgB,KAAK,CAAC,CAAC,EACrB,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,KAAK,CAAC,EAAE,MAAM,EACd,GAAG,CAAC,EAAE,MAAM,GACX,OAAO,CAAC,CAAC,CAAC,CAEZ;AAED;;;;;;;;;;;;;;GAcG;AACH,wBAAgB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAEzD;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,GAAG,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,OAAO,EACtD,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,EACpC,OAAO,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,GAC5B,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAIhB;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,OAAO,CAAC,CAAC,SAAS,OAAO,EACvC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAChB,QAAQ,CAAC,EAAE,MAAM,GAChB,OAAO,CAAC,OAAO,CAAC,CAIlB"}