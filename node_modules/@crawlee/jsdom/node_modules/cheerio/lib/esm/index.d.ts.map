{"version": 3, "file": "index.d.ts", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["index.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;AACH,YAAY,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAE5C;;;;GAIG;AACH,cAAc,YAAY,CAAC;AAC3B,YAAY,EACV,cAAc,EACd,kBAAkB,EAClB,aAAa,GACd,MAAM,cAAc,CAAC;AACtB;;;;GAIG;AACH,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAE/E,YAAY,EAAE,UAAU,EAAE,MAAM,WAAW,CAAC;AAc5C;;;;;;;;;;;;GAYG;AACH,eAAO,MAAM,IAAI,wNAIhB,CAAC;AAEF;;;;GAIG;;AACH,wBAAwB;AAExB,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,aAAa,CAAC;AAE9C,OAAO,KAAK,aAAa,MAAM,aAAa,CAAC;AAE7C;;;;;;;;;;;;;;;;;;GAkBG;AACH,eAAO,MAAQ,QAAQ,+BAAkB,CAAC;AAE1C;;;;;;;;;;;;;GAaG;AACH,eAAO,MAAQ,KAAK,4BAAkB,CAAC;AAEvC;;;;;;;;;;;;GAYG;AACH,eAAO,MAAQ,SAAS,gCAAkB,CAAC;AAE3C;;;;;;;;;;;GAWG;AACH,eAAO,MAAQ,IAAI,2BAAkB,CAAC"}