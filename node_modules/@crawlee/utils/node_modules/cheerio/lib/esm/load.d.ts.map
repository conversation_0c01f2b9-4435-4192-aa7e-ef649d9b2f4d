{"version": 3, "file": "load.d.ts", "sourceRoot": "https://raw.githubusercontent.com/cheeriojs/cheerio/d1cbc66d53392ce8bf6cd0068f675836372d2bf3/src/", "sources": ["load.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,cAAc,EACd,eAAe,EAGhB,MAAM,cAAc,CAAC;AACtB,OAAO,KAAK,aAAa,MAAM,aAAa,CAAC;AAC7C,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAEvC,OAAO,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAc,MAAM,YAAY,CAAC;AACzE,OAAO,KAAK,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,YAAY,CAAC;AAEnE,aAAK,UAAU,GAAG,OAAO,aAAa,CAAC;AAEvC;;;;GAIG;AACH,MAAM,WAAW,UAAW,SAAQ,UAAU;IAC5C;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,CAAC,CAAC,SAAS,OAAO,EAAE,CAAC,SAAS,MAAM,EAClC,QAAQ,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,EACpC,OAAO,CAAC,EAAE,kBAAkB,CAAC,OAAO,CAAC,GAAG,IAAI,EAC5C,IAAI,CAAC,EAAE,kBAAkB,CAAC,QAAQ,CAAC,EACnC,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,CAAC,SAAS,YAAY,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;IAEjD;;;;OAIG;IACH,KAAK,EAAE,QAAQ,CAAC;IAEhB;;;;OAIG;IACH,QAAQ,EAAE,eAAe,CAAC;IAE1B,yDAAyD;IACzD,EAAE,EAAE,OAAO,OAAO,CAAC,SAAS,CAAC;IAE7B,IAAI,EAAE,UAAU,CAAC,OAAO,OAAO,CAAC,CAAC;CAClC;AAED,wBAAgB,OAAO,CACrB,KAAK,EAAE,OAAO,OAAO,CAAC,SAAS,CAAC,MAAM,EACtC,MAAM,EAAE,CACN,GAAG,EAAE,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,EACjC,OAAO,EAAE,eAAe,KACrB,MAAM,aAgBA,MAAM,GAAG,OAAO,GAAG,OAAO,EAAE,GAAG,MAAM,YACpC,cAAc,GAAG,IAAI,2BAE9B,UAAU,CAyId"}