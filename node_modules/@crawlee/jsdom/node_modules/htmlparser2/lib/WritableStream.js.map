{"version": 3, "file": "WritableStream.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/c123610e003a1eaebc61febed01cabb6e41eb658/src/", "sources": ["WritableStream.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,yCAA6D;AAC7D;;;GAGG;AACH,2CAAuC;AACvC,2DAAoD;AAEpD,2GAA2G;AAC3G,SAAS,QAAQ,CAAC,MAAuB,EAAE,QAAgB;IACvD,OAAO,QAAQ,KAAK,QAAQ,CAAC;AACjC,CAAC;AAED;;;;GAIG;AACH;IAAoC,kCAAQ;IAIxC,wBAAY,GAAqB,EAAE,OAAuB;QAA1D,YACI,kBAAM,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,SAElC;QALgB,cAAQ,GAAG,IAAI,mCAAa,EAAE,CAAC;QAI5C,KAAI,CAAC,OAAO,GAAG,IAAI,kBAAM,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;;IAC5C,CAAC;IAEQ,+BAAM,GAAf,UACI,KAAsB,EACtB,QAAgB,EAChB,QAAoB;QAEpB,IAAI,CAAC,OAAO,CAAC,KAAK,CACd,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CACjE,CAAC;QACF,QAAQ,EAAE,CAAC;IACf,CAAC;IAEQ,+BAAM,GAAf,UAAgB,QAAoB;QAChC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;QACtC,QAAQ,EAAE,CAAC;IACf,CAAC;IACL,qBAAC;AAAD,CAAC,AAxBD,CAAoC,sBAAQ,GAwB3C;AAxBY,wCAAc"}