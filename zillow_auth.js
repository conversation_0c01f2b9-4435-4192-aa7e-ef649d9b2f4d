// zillow_auth.js
// Focused script to authenticate with <PERSON><PERSON><PERSON> and save authenticated state

import playwright from 'playwright';
import { config } from './scraper-config.js';
import fs from 'node:fs/promises';
import path from 'node:path';

function getRandomUserAgent() {
  return config.userAgents[Math.floor(Math.random() * config.userAgents.length)];
}

async function solveHumanChallenge(page, log = console) {
  for (let attempt = 0; attempt < 5; attempt++) {
    await page.waitForTimeout(1000 + Math.random() * 2000);

    const btn = await page.$('text=Press and Hold');
    if (!btn) {
      if (attempt === 0) {
        log.info('No human challenge detected on this attempt');
      }
      return true;
    }

    // Challenge found, solve it
    log.info(`HUMAN challenge detected – solving… (attempt ${attempt + 1})`);

    const box = await btn.boundingBox();
    if (!box) {
      log.warn('Challenge button found but no bounding box');
      continue;
    }

    const cx = box.x + box.width/2;
    const cy = box.y + box.height/2;

    // Human-like mouse movement
    await page.mouse.move(
      cx + (Math.random() - 0.5) * 20,
      cy + (Math.random() - 0.5) * 20,
      { steps: 15 + Math.floor(Math.random() * 10) }
    );

    await page.waitForTimeout(500 + Math.random() * 1000);
    await page.mouse.move(cx, cy, { steps: 5 });
    await page.waitForTimeout(200 + Math.random() * 300);

    await page.mouse.down();
    log.info('Holding button...');

    const holdTime = 6000 + Math.random() * 3000;
    const start = Date.now();
    let moveCount = 0;

    while (Date.now() - start < holdTime) {
      if (moveCount % 10 === 0) {
        await page.mouse.move(
          cx + (Math.random() - 0.5) * 2,
          cy + (Math.random() - 0.5) * 2,
          { steps: 1 }
        );
      }
      moveCount++;
      await page.waitForTimeout(150 + Math.random() * 100);
    }

    await page.mouse.up();
    log.info('Released button, waiting for verification...');

    // Check if challenge was solved
    await page.waitForTimeout(3000 + Math.random() * 2000);
    const stillPresent = await page.$('text=Press and Hold');
    if (!stillPresent) {
      log.info('✅ Human challenge solved successfully!');
      return true;
    }
  }
  return false;
}

async function authenticateWithZillow() {
  // Try multiple times to authenticate
  for (let authAttempt = 0; authAttempt < 3; authAttempt++) {
    console.log(`Authentication attempt ${authAttempt + 1}/3`);

    const browser = await playwright.chromium.launch({
      headless: false,
      args: [
        ...config.browserArgs,
        '--disable-blink-features=AutomationControlled'
      ]
    });

    const context = await browser.newContext({
      userAgent: getRandomUserAgent(),
      viewport: {
        width: 1366 + Math.floor(Math.random() * 200),
        height: 768 + Math.floor(Math.random() * 200)
      },
      locale: 'en-US',
      timezoneId: 'America/New_York',
      geolocation: { latitude: 40.7128, longitude: -74.0060 },
      permissions: ['geolocation']
    });

    // Apply stealth techniques
    await context.addInitScript(() => {
      // Hide webdriver
      Object.defineProperty(navigator, 'webdriver', { get: () => undefined });

      // Add chrome properties
      window.chrome = {
        runtime: {},
        loadTimes: () => {},
        csi: () => {},
        app: {}
      };

      // Add plugins
      Object.defineProperty(navigator, 'plugins', {
        get: () => [
          { name: 'Chrome PDF Plugin' },
          { name: 'Chrome PDF Viewer' },
          { name: 'Native Client' }
        ]
      });

      // Override permissions
      const originalQuery = window.navigator.permissions.query;
      window.navigator.permissions.query = (parameters) => (
        parameters.name === 'notifications' ?
          Promise.resolve({ state: Notification.permission }) :
          originalQuery(parameters)
      );
    });

    const page = await context.newPage();

    try {
      // Set extra headers to appear more like a real browser
      await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
      });

      console.log('Visiting Zillow homepage to warm up session...');
      await page.goto('https://www.zillow.com', {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      // Wait for page to stabilize
      await page.waitForTimeout(3000 + Math.random() * 2000);

      // Simulate human behavior - move mouse randomly
      await page.mouse.move(Math.random() * 500, Math.random() * 500, { steps: 10 });
      await page.waitForTimeout(1000 + Math.random() * 1000);

      // Scroll down a bit
      await page.evaluate(() => window.scrollTo(0, Math.random() * 300));
      await page.waitForTimeout(2000 + Math.random() * 1000);

      // Try to solve any challenges
      let challengeSolved = await solveHumanChallenge(page);
      if (!challengeSolved) {
        console.log('Challenge detected on homepage, trying again...');
        await browser.close();
        continue; // Try again with a new browser session
      }

      // Visit a profile page to ensure we're authenticated
      console.log('Visiting a profile page to verify authentication...');
      await page.goto('https://www.zillow.com/profile/mccannteam', {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      // Wait for page to load
      await page.waitForTimeout(5000);

      // Check for challenge again
      challengeSolved = await solveHumanChallenge(page);
      if (!challengeSolved) {
        console.log('Challenge detected on profile page, trying again...');
        await browser.close();
        continue; // Try again with a new browser session
      }

      // Wait for page to fully load
      try {
        await page.waitForLoadState('networkidle', { timeout: 10000 });
      } catch (e) {
        console.log('Page did not reach networkidle state, continuing anyway');
      }

      // Verify we're on the right page by checking title or content
      const title = await page.title();
      console.log(`Page title: ${title}`);

      if (!title.includes('Profile') && !title.includes('Agent')) {
        console.log('Not on profile page, may be blocked. Trying again...');
        await browser.close();
        continue;
      }

      // Save authenticated state
      const authDir = './playwright/.auth';
      await fs.mkdir(authDir, { recursive: true });
      await context.storageState({ path: path.join(authDir, 'zillow_auth.json') });
      console.log('✅ Authentication successful! State saved to playwright/.auth/zillow_auth.json');

      // Keep browser open a bit longer to let any final scripts run
      console.log('Keeping browser open for 10 seconds...');
      await page.waitForTimeout(10000);
      await browser.close();

      return true;
    } catch (error) {
      console.error('Error during authentication:', error);
      await browser.close();

      // Wait between attempts
      if (authAttempt < 2) {
        const waitTime = 10000 + Math.random() * 5000;
        console.log(`Waiting ${Math.round(waitTime/1000)} seconds before next attempt...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  console.error('Failed to authenticate after 3 attempts');
  return false;
}

// Run the authentication
await authenticateWithZillow();
